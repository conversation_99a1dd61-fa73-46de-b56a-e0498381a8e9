﻿# ===== PSREADLINE CONFIGURATION =====
Set-PSReadLineOption -PredictionSource History
Set-PSReadLineOption -PredictionViewStyle InlineView
Set-PSReadLineKeyHandler -Key Tab -Function MenuComplete
Set-PSReadLineKeyHandler -Key UpArrow -Function HistorySearchBackward
Set-PSReadLineKeyHandler -Key DownArrow -Function HistorySearchForward

# ===== FZF INTEGRATION =====
try {
    Import-Module PSFzf -ErrorAction SilentlyContinue
    Set-PsFzfOption -PSReadlineChordProvider 'Ctrl+f' -PSReadlineChordReverseHistory 'Ctrl+r'
    Write-Host "✅ PSFzf loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ PSFzf not available" -ForegroundColor Yellow
}

# ===== ZOXIDE INTEGRATION =====
try {
    # Test if zoxide is available
    zoxide --version | Out-Null
    
    # Create z function for directory jumping
    function global:z {
        param($query)
        if ($query) {
            $path = zoxide query $query 2>$null
            if ($path -and (Test-Path $path)) { 
                Set-Location $path 
                zoxide add $path
            } else {
                Write-Host "No match found for: $query" -ForegroundColor Yellow
            }
        } else {
            # Interactive mode
            $path = zoxide query -i 2>$null
            if ($path -and (Test-Path $path)) { 
                Set-Location $path 
            }
        }
    }
    
    # Create zi function for interactive search
    function global:zi {
        $path = zoxide query -i 2>$null
        if ($path -and (Test-Path $path)) { 
            Set-Location $path 
        }
    }
    
    Write-Host "✅ Zoxide loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Zoxide not available" -ForegroundColor Yellow
}

# ===== ADDITIONAL ENHANCEMENTS =====
Set-PSReadLineOption -Colors @{
    Command = 'Yellow'; Parameter = 'Green'; Operator = 'Magenta'
    Variable = 'Green'; String = 'Blue'; Number = 'Blue'
    Type = 'Cyan'; Comment = 'DarkGreen'
}
Set-PSReadLineOption -ShowToolTips
Set-PSReadLineOption -MaximumHistoryCount 4000

# ===== CUSTOM FUNCTIONS =====
function .. { Set-Location .. }
function ... { Set-Location ..\.. }
function .... { Set-Location ..\..\.. }
function gs { git status }
function ga { git add . }
function gc { param($message) git commit -m $message }
function gp { git push }
function gl { git log --oneline -10 }
function ll { Get-ChildItem -Force }
function la { Get-ChildItem -Force -Hidden }

Write-Host "🚀 PowerShell Profile Loaded Successfully!" -ForegroundColor Cyan
Write-Host "Features enabled:" -ForegroundColor White
Write-Host "  • Predictive suggestions (ghost text)" -ForegroundColor Gray
Write-Host "  • Enhanced tab completion" -ForegroundColor Gray
Write-Host "  • Fuzzy search (Ctrl+F, Ctrl+R)" -ForegroundColor Gray
Write-Host "  • Smart directory jumping (z, zi commands)" -ForegroundColor Gray
Write-Host "  • Syntax highlighting" -ForegroundColor Gray
Write-Host "  • Custom shortcuts (gs, ga, gc, gp, ll, etc.)" -ForegroundColor Gray
