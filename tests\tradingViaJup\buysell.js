const axios = require("axios");

const BEARER_TOKEN = "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eyJzaWQiOiJjbWVxd29qaTgwMHhnanMwYjgxODQ2NGQzIiwiaXNzIjoicHJpdnkuaW8iLCJpYXQiOjE3NTYxMTM1NzYsImF1ZCI6ImNtZTd3aHY3ZTAwM3FreTBhcGkxeHRpcjQiLCJzdWIiOiJkaWQ6cHJpdnk6Y21lcXVnOTI1MDBnNWw1MGJmcXhveWYxayIsImV4cCI6MTc1NjExNzE3Nn0.fC5J4tIKUqnoCy1EPE4BHHJ9gUaBeWbU8FVz0tvTC4UMoql2tGa0QMKOjOl8Kw4Nk27T5k9Tdy9NJrJl9Q4HMg"; // dán token ở đây

async function buyToken() {
  try {
    const response = await axios.post(
      "https://api.dev.cashdrop.click/trades-v2/buysell/v2",
      [
        {
          type: "trading",
          side: "buy",
          unit: "amount",
          walletAddress: "BuNjqkRrqnRWkid3HPrUqzJHTLn3AwR4TFfiyPTr8Qpq",
          baseToken: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
          symbol: "RYS",
          amount: 0.0001,
          slippage: 20,
          priorityFee: 0.00001,
          tipAmount: 0,
        },
      ],
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${BEARER_TOKEN}`,
          Origin: "https://dev.cashdrop.click",
          Referer: "https://dev.cashdrop.click/",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
      }
    );

    console.log("Response:", response.data);
  } catch (err) {
    if (err.response) {
      console.error("Error:", err.response.status, err.response.data);
    } else {
      console.error("Request error:", err.message);
    }
  }
}

buyToken();
